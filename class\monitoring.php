<?php
    require_once '../includes/session.php';
    require_once '../core/dbcon.ini';
    
    $page_title = "Class Teacher Monitoring";
    $current_page = "Monitoring";

    // Get current teacher info
    $teacher_id = $_SESSION['uaid'];
    $teacher_name = $_SESSION['name'];

    // Get attendance logs for current teacher
    function getTeacherAttendanceLogs($db, $teacher_id, $limit = 10) {
        // Check if class_attendance_logs table exists
        $checkTable = $db->prepare("SHOW TABLES LIKE 'class_attendance_logs'");
        $checkTable->execute();

        if ($checkTable->rowCount() == 0) {
            // Return empty array if table doesn't exist
            return [];
        }

        $stmt = $db->prepare("
            SELECT
                DATE(created_at) as log_date,
                COUNT(*) as entries_count,
                GROUP_CONCAT(DISTINCT subject_code ORDER BY subject_code) as subjects
            FROM class_attendance_logs
            WHERE teacher_id = ?
            GROUP BY DATE(created_at)
            ORDER BY log_date DESC
            LIMIT ?
        ");
        $stmt->bindValue(1, $teacher_id, PDO::PARAM_INT);
        $stmt->bindValue(2, $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get available courses and years for filtering
    function getCoursesAndYears($db) {
        $stmt = $db->prepare("
            SELECT DISTINCT 
                Course,
                Year,
                CONCAT(Course, ' ', Year) as course_year
            FROM cdas_student 
            ORDER BY Course, Year
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get attendance records with filters
    function getAttendanceRecords($db, $course = null, $year = null, $date = null) {
        $sql = "
            SELECT 
                s.Student_ID,
                s.First_Name,
                s.Last_Name,
                s.Course,
                s.Year,
                a.time_in,
                a.time_out,
                DATE(a.time_in) as attendance_date
            FROM cdas_student s
            LEFT JOIN cdas_attendance a ON s.Student_ID = a.Student_ID
            WHERE 1=1
        ";
        
        $params = [];
        
        if ($course) {
            $sql .= " AND s.Course = ?";
            $params[] = $course;
        }
        
        if ($year) {
            $sql .= " AND s.Year = ?";
            $params[] = $year;
        }
        
        if ($date) {
            $sql .= " AND DATE(a.time_in) = ?";
            $params[] = $date;
        }
        
        $sql .= " ORDER BY s.Course, s.Year, s.Last_Name, s.First_Name";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Handle AJAX requests for getting attendance
    if (isset($_POST['action']) && $_POST['action'] == 'get_attendance') {
        $course = $_POST['course'] ?? null;
        $year = $_POST['year'] ?? null;
        $date = $_POST['date'] ?? null;
        
        $records = getAttendanceRecords($db1, $course, $year, $date);
        
        header('Content-Type: application/json');
        echo json_encode($records);
        exit;
    }

    // Get data for display
    $logs = getTeacherAttendanceLogs($db1, $teacher_id);
    $coursesYears = getCoursesAndYears($db1);

    // Check if setup is needed
    $needsSetup = empty($logs) && !isset($_POST['action']);
?>

<?php include '../assets/html/header.php'; ?>

<body>
    <!-- ======== Preloader =========== -->
    <div id="preloader">
        <div class="spinner"></div>
    </div>
    <!-- ======== Preloader =========== -->

    <!-- ======== sidebar-nav start =========== -->
    <?php include '../assets/html/sidebar.php'; ?>
    <!-- ======== sidebar-nav end =========== -->

    <!-- ======== main-wrapper start =========== -->
    <main class="main-wrapper">
        <!-- ========== header start ========== -->
        <?php include '../assets/html/topbar.php'; ?>
        <!-- ========== header end ========== -->

        <!-- ========== section start ========== -->
        <section class="section">
            <div class="container-fluid">
                <!-- ========== title-wrapper start ========== -->
                <div class="title-wrapper pt-30">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="title">
                                <h2>Class Teacher Monitoring</h2>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="breadcrumb-wrapper">
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb">
                                        <li class="breadcrumb-item">
                                            <a href="#0">My Classes</a>
                                        </li>
                                        <li class="breadcrumb-item active" aria-current="page">
                                            Monitoring
                                        </li>
                                    </ol>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- ========== title-wrapper end ========== -->

                <?php if ($needsSetup): ?>
                <!-- Setup Notice -->
                <div class="row">
                    <div class="col-12">
                        <div class="alert-box primary-alert mb-30" style="background-color: #e3f2fd; border-left: 4px solid #004AAD; padding: 20px; border-radius: 8px;">
                            <div class="alert">
                                <h4 class="alert-heading" style="color: #004AAD; margin-bottom: 10px;">
                                    <i class="lni lni-information"></i> Setup Required
                                </h4>
                                <p class="mb-15" style="color: #666;">
                                    Welcome to the Class Teacher Monitoring System! To get started, you need to set up the database tables and sample data.
                                </p>
                                <a href="setup_database.php" class="main-btn primary-btn btn-hover" style="background-color: #004AAD; border-color: #004AAD; border-radius: 8px; padding: 10px 20px; text-decoration: none; color: white;">
                                    <i class="lni lni-cog"></i> Run Database Setup
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Teacher Activity Logs -->
                    <div class="col-lg-6">
                        <div class="card-style mb-30">
                            <div class="title d-flex justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2">My Activity Logs</h6>
                                    <p class="text-sm text-gray">Recent attendance entries</p>
                                </div>
                                <div class="right">
                                    <div class="select-style-1">
                                        <select id="logFilter">
                                            <option value="10">Last 10 days</option>
                                            <option value="30">Last 30 days</option>
                                            <option value="90">Last 3 months</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="table-wrapper table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Entries</th>
                                            <th>Subjects</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($logs)): ?>
                                            <tr>
                                                <td colspan="3" class="text-center text-gray">
                                                    No activity logs found
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($logs as $log): ?>
                                                <tr>
                                                    <td>
                                                        <p class="text-sm"><?php echo date('M d, Y', strtotime($log['log_date'])); ?></p>
                                                    </td>
                                                    <td>
                                                        <span class="status-btn info-btn"><?php echo $log['entries_count']; ?> entries</span>
                                                    </td>
                                                    <td>
                                                        <p class="text-sm"><?php echo $log['subjects'] ?? 'N/A'; ?></p>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Get Attendance Section -->
                    <div class="col-lg-6">
                        <div class="card-style mb-30">
                            <div class="title d-flex justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2">Get Attendance Records</h6>
                                    <p class="text-sm text-gray">Filter by course, year, and date</p>
                                </div>
                            </div>

                            <form id="attendanceForm">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="select-style-1 mb-20">
                                            <label>Course & Year</label>
                                            <select id="courseYear" name="course_year">
                                                <option value="">Select Course & Year</option>
                                                <?php foreach ($coursesYears as $cy): ?>
                                                    <option value="<?php echo $cy['Course'] . '|' . $cy['Year']; ?>">
                                                        <?php echo $cy['course_year']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="input-style-1 mb-20">
                                            <label>Date</label>
                                            <input type="date" id="attendanceDate" name="date" value="<?php echo date('Y-m-d'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <button type="button" class="main-btn primary-btn btn-hover w-100" onclick="getAttendance()" style="background-color: #004AAD; border-color: #004AAD; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 500;">
                                            Get Attendance Records
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Attendance Results -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-style mb-30" id="attendanceResults" style="display: none;">
                            <div class="title d-flex justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2">Attendance Records</h6>
                                    <p class="text-sm text-gray" id="resultsInfo"></p>
                                </div>
                                <div class="right">
                                    <button class="main-btn success-btn btn-hover" onclick="exportAttendance()" style="background-color: #28a745; border-color: #28a745; border-radius: 8px; padding: 8px 16px; font-size: 12px;">
                                        Export to Excel
                                    </button>
                                </div>
                            </div>

                            <div class="table-wrapper table-responsive">
                                <table class="table" id="attendanceTable">
                                    <thead>
                                        <tr>
                                            <th>Student ID</th>
                                            <th>Name</th>
                                            <th>Course & Year</th>
                                            <th>Time In</th>
                                            <th>Time Out</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="attendanceTableBody">
                                        <!-- Results will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- ========== section end ========== -->

        <!-- ========== footer start =========== -->
        <?php include '../assets/html/footer.php'; ?>
        <!-- ========== footer end =========== -->
    </main>
    <!-- ======== main-wrapper end =========== -->

    <!-- ========= All Javascript files linkup ======== -->
    <?php include '../assets/html/scripts.php'; ?>

    <script>
        function getAttendance() {
            const courseYear = document.getElementById('courseYear').value;
            const date = document.getElementById('attendanceDate').value;
            
            if (!courseYear) {
                alert('Please select a course and year');
                return;
            }
            
            const [course, year] = courseYear.split('|');
            
            // Show loading
            document.getElementById('attendanceResults').style.display = 'block';
            document.getElementById('attendanceTableBody').innerHTML = '<tr><td colspan="6" class="text-center">Loading...</td></tr>';
            
            // Make AJAX request
            fetch('monitoring.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_attendance&course=${course}&year=${year}&date=${date}`
            })
            .then(response => response.json())
            .then(data => {
                displayAttendanceResults(data, course, year, date);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('attendanceTableBody').innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading data</td></tr>';
            });
        }
        
        function displayAttendanceResults(records, course, year, date) {
            const tbody = document.getElementById('attendanceTableBody');
            const resultsInfo = document.getElementById('resultsInfo');
            
            resultsInfo.textContent = `${course} ${year} - ${new Date(date).toLocaleDateString()} (${records.length} students)`;
            
            if (records.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-gray">No records found</td></tr>';
                return;
            }
            
            tbody.innerHTML = records.map(record => {
                const status = record.time_in ? (record.time_out ? 'Complete' : 'Present') : 'Absent';
                const statusClass = status === 'Complete' ? 'success-btn' : status === 'Present' ? 'warning-btn' : 'danger-btn';
                
                return `
                    <tr>
                        <td>${record.Student_ID}</td>
                        <td>${record.Last_Name}, ${record.First_Name}</td>
                        <td>${record.Course} ${record.Year}</td>
                        <td>${record.time_in ? new Date(record.time_in).toLocaleTimeString() : '-'}</td>
                        <td>${record.time_out ? new Date(record.time_out).toLocaleTimeString() : '-'}</td>
                        <td><span class="status-btn ${statusClass}">${status}</span></td>
                    </tr>
                `;
            }).join('');
        }
        
        function exportAttendance() {
            // Simple CSV export functionality
            const table = document.getElementById('attendanceTable');
            let csv = [];
            
            // Get headers
            const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent);
            csv.push(headers.join(','));
            
            // Get data rows
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            rows.forEach(row => {
                const cells = Array.from(row.querySelectorAll('td')).map(td => td.textContent.trim());
                csv.push(cells.join(','));
            });
            
            // Download CSV
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `attendance_${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
