<?php
    include '../core/dbcon.ini';
    include '../assets/common/header.php';
    include '../assets/common/design.php';

    $page_title = "Class Teacher Monitoring";
    $current_page = "Monitoring & Logs";

    // Hardcoded teacher activity logs with student attendance data
    $teacher_logs = [
        [
            'date' => '2025-06-24',
            'entries' => 45,
            'subjects' => 'IT402, IT624, IT271',
            'courses' => 'BSIT 3, Automotive 1',
            'students' => [
                ['name' => '<PERSON>', 'subject' => 'IT402', 'time_in' => '08:15 AM', 'status' => 'Present'],
                ['name' => '<PERSON>', 'subject' => 'IT402', 'time_in' => '08:20 AM', 'status' => 'Late'],
                ['name' => '<PERSON>', 'subject' => 'IT624', 'time_in' => '10:10 AM', 'status' => 'Present'],
                ['name' => '<PERSON><PERSON><PERSON>', 'subject' => 'IT624', 'time_in' => '10:25 AM', 'status' => 'Late'],
                ['name' => 'Jimwe<PERSON>', 'subject' => 'IT271', 'time_in' => '02:05 PM', 'status' => 'Present'],
                ['name' => 'Mark Santos', 'subject' => 'IT271', 'time_in' => '02:15 PM', 'status' => 'Present']
            ]
        ],
        [
            'date' => '2025-06-23',
            'entries' => 38,
            'subjects' => 'IT421, IT291, IT621',
            'courses' => 'BSIT 3, Automotive 2',
            'students' => [
                ['name' => 'Glenn Mark Ruz', 'subject' => 'IT421', 'time_in' => '01:10 PM', 'status' => 'Present'],
                ['name' => 'Juan Dela Cruz', 'subject' => 'IT421', 'time_in' => '01:20 PM', 'status' => 'Late'],
                ['name' => 'Maria Garcia', 'subject' => 'IT291', 'time_in' => '11:05 AM', 'status' => 'Present'],
                ['name' => 'Pedro Reyes', 'subject' => 'IT621', 'time_in' => '03:15 PM', 'status' => 'Present']
            ]
        ],
        [
            'date' => '2025-06-22',
            'entries' => 42,
            'subjects' => 'IT431, IT321, NSTP2',
            'courses' => 'BSIT 3, Automotive 1',
            'students' => [
                ['name' => 'John Ralph Jordico', 'subject' => 'IT431', 'time_in' => '01:05 PM', 'status' => 'Present'],
                ['name' => 'Nessa Mae Mata', 'subject' => 'IT431', 'time_in' => '01:15 PM', 'status' => 'Late'],
                ['name' => 'Ana Villanueva', 'subject' => 'IT321', 'time_in' => '08:10 AM', 'status' => 'Present'],
                ['name' => 'Carlos Mendoza', 'subject' => 'NSTP2', 'time_in' => '07:30 AM', 'status' => 'Present']
            ]
        ],
        [
            'date' => '2025-06-21',
            'entries' => 35,
            'subjects' => 'IT106, IT221, IT241',
            'courses' => 'BSIT 3, Automotive 3',
            'students' => [
                ['name' => 'Jimwel Manguiat', 'subject' => 'IT106', 'time_in' => '03:05 PM', 'status' => 'Present'],
                ['name' => 'Lisa Torres', 'subject' => 'IT221', 'time_in' => '02:10 PM', 'status' => 'Present'],
                ['name' => 'Mark Santos', 'subject' => 'IT241', 'time_in' => '09:15 AM', 'status' => 'Late']
            ]
        ],
        [
            'date' => '2025-06-20',
            'entries' => 40,
            'subjects' => 'IT311, IT402, IT624',
            'courses' => 'BSIT 3, Automotive 2',
            'students' => [
                ['name' => 'Glenn Mark Ruz', 'subject' => 'IT311', 'time_in' => '04:05 PM', 'status' => 'Present'],
                ['name' => 'Joe Vert Matienzo', 'subject' => 'IT402', 'time_in' => '08:25 AM', 'status' => 'Late'],
                ['name' => 'Juan Dela Cruz', 'subject' => 'IT624', 'time_in' => '10:15 AM', 'status' => 'Present']
            ]
        ]
    ];

    // Hardcoded courses and years for filtering
    $courses_years = [
        'BSIT 1' => 'BSIT 1st Year',
        'BSIT 2' => 'BSIT 2nd Year', 
        'BSIT 3' => 'BSIT 3rd Year',
        'BSIT 4' => 'BSIT 4th Year',
        'Automotive 1' => 'Automotive 1st Year',
        'Automotive 2' => 'Automotive 2nd Year',
        'Automotive 3' => 'Automotive 3rd Year',
        'Automotive 4' => 'Automotive 4th Year',
        'Electrical 1' => 'Electrical 1st Year',
        'Electrical 2' => 'Electrical 2nd Year',
        'Mechanical 1' => 'Mechanical 1st Year',
        'Mechanical 2' => 'Mechanical 2nd Year'
    ];
?>

<body>
<?php include '../assets/common/title.php'; ?>

<div class="main-container" id="main-container">
    <?php include '../assets/common/sidebar.php'; ?>
</div>

<main class="main-wrapper">
    <?php require '../assets/common/topbar.php'; ?>

    <section class="section">
        <div class="container-fluid">
            <!-- ========== title-wrapper start ========== -->
            <div class="title-wrapper pt-10">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="title">
                            <h2 style="font-size: 24px;"><?php echo $page_title; ?></h2>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="breadcrumb-wrapper">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="index.php">My Classes</a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php echo $current_page; ?>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
                <!-- end row -->

                <!-- Back Button -->
                <div class="row mb-20">
                    <div class="col-12">
                        <a href="index.php" class="main-btn secondary-btn btn-hover" style="background-color: #6c757d; border-color: #6c757d; border-radius: 8px; padding: 10px 20px; font-size: 14px; text-decoration: none; color: white;">
                            ← Back to Dashboard
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Teacher Activity Logs - Automatic Display -->
                    <div class="col-lg-8">
                        <div class="card-style mb-30">
                            <div class="title d-flex justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2" style="font-size: 18px;"><i class="lni lni-calendar"></i> My Activity Logs</h6>
                                    <p class="text-sm text-gray" style="font-size: 14px;">Click on any date to view students who attended your classes</p>
                                </div>
                                <div class="right">
                                    <span class="status-btn success-btn" style="background-color: #4CAF50; color: white; padding: 6px 12px; border-radius: 6px; font-size: 12px;">
                                        <i class="lni lni-checkmark-circle"></i> Live Updates
                                    </span>
                                </div>
                            </div>

                            <div class="table-wrapper table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th style="font-size: 14px; font-weight: 600;">Date</th>
                                            <th style="font-size: 14px; font-weight: 600;">Total Entries</th>
                                            <th style="font-size: 14px; font-weight: 600;">Subjects Handled</th>
                                            <th style="font-size: 14px; font-weight: 600;">Courses</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($teacher_logs as $index => $log): ?>
                                            <tr style="cursor: pointer;" onclick="viewStudents('<?php echo $log['date']; ?>', <?php echo $index; ?>)" onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor=''">
                                                <td>
                                                    <p class="text-sm" style="font-size: 14px; margin: 0; color: #004AAD; font-weight: 500;">
                                                        <i class="lni lni-eye"></i> <?php echo date('M d, Y', strtotime($log['date'])); ?>
                                                    </p>
                                                    <p class="text-gray" style="font-size: 12px; margin: 0;">
                                                        <?php echo date('l', strtotime($log['date'])); ?> - Click to view students
                                                    </p>
                                                </td>
                                                <td>
                                                    <span class="status-btn info-btn" style="background-color: #004AAD; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                                        <i class="lni lni-users"></i> <?php echo $log['entries']; ?> entries
                                                    </span>
                                                </td>
                                                <td>
                                                    <p class="text-sm" style="font-size: 13px; margin: 0; line-height: 1.4;">
                                                        <i class="lni lni-book"></i> <?php echo $log['subjects']; ?>
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="text-sm" style="font-size: 13px; margin: 0; line-height: 1.4;">
                                                        <i class="lni lni-graduation"></i> <?php echo $log['courses']; ?>
                                                    </p>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="col-lg-4">
                        <div class="card-style mb-30">
                            <div class="title d-flex justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2" style="font-size: 18px;">📈 Quick Stats</h6>
                                    <p class="text-sm text-gray" style="font-size: 14px;">This week summary</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="single-report mb-20" style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
                                        <div class="s-report-inner">
                                            <div class="icon" style="margin-bottom: 8px;">
                                                <i class="lni lni-pencil" style="font-size: 24px; color: #004AAD;"></i>
                                            </div>
                                            <div class="s-report-title d-flex justify-content-between">
                                                <h4 class="header-title" style="font-size: 20px; color: #004AAD; margin: 0;">200</h4>
                                            </div>
                                            <div class="s-report-des">
                                                <span style="font-size: 12px; color: #666;">Total Entries</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="single-report mb-20" style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
                                        <div class="s-report-inner">
                                            <div class="icon" style="margin-bottom: 8px;">
                                                <i class="lni lni-book" style="font-size: 24px; color: #4CAF50;"></i>
                                            </div>
                                            <div class="s-report-title d-flex justify-content-between">
                                                <h4 class="header-title" style="font-size: 20px; color: #4CAF50; margin: 0;">13</h4>
                                            </div>
                                            <div class="s-report-des">
                                                <span style="font-size: 12px; color: #666;">Subjects</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="single-report mb-20" style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
                                        <div class="s-report-inner">
                                            <div class="icon" style="margin-bottom: 8px;">
                                                <i class="lni lni-graduation" style="font-size: 24px; color: #FFC300;"></i>
                                            </div>
                                            <div class="s-report-title d-flex justify-content-between">
                                                <h4 class="header-title" style="font-size: 20px; color: #FFC300; margin: 0;">8</h4>
                                            </div>
                                            <div class="s-report-des">
                                                <span style="font-size: 12px; color: #666;">Courses</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="single-report mb-20" style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
                                        <div class="s-report-inner">
                                            <div class="icon" style="margin-bottom: 8px;">
                                                <i class="lni lni-users" style="font-size: 24px; color: #FF4B4B;"></i>
                                            </div>
                                            <div class="s-report-title d-flex justify-content-between">
                                                <h4 class="header-title" style="font-size: 20px; color: #FF4B4B; margin: 0;">385</h4>
                                            </div>
                                            <div class="s-report-des">
                                                <span style="font-size: 12px; color: #666;">Students</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="mt-20">
                                <h6 style="font-size: 14px; margin-bottom: 15px;"><i class="lni lni-cog"></i> Quick Actions:</h6>
                                <div class="d-grid gap-2">
                                    <a href="get_attendance.php" class="main-btn success-btn btn-hover w-100" style="background-color: #4CAF50; border-color: #4CAF50; border-radius: 8px; padding: 10px; font-size: 13px; text-decoration: none; color: white;">
                                        <i class="lni lni-clipboard"></i> Get Attendance Records
                                    </a>
                                    <button class="main-btn warning-btn btn-hover w-100" style="background-color: #FFC300; border-color: #FFC300; border-radius: 8px; padding: 10px; font-size: 13px; color: #333;">
                                        <i class="lni lni-download"></i> Export Reports
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
    <?php include '../assets/common/footer.php'; ?>
</main>

<!-- Student Details Modal -->
<div class="modal fade" id="studentsModal" tabindex="-1" aria-labelledby="studentsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #004AAD; color: white;">
                <h5 class="modal-title" id="studentsModalLabel" style="font-size: 18px;">
                    <i class="lni lni-users"></i> Students Who Attended Your Classes
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="modalContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="border-radius: 8px;">
                    <i class="lni lni-cross-circle"></i> Close
                </button>
                <button type="button" class="btn btn-primary" onclick="exportStudentList()" style="background-color: #004AAD; border-color: #004AAD; border-radius: 8px;">
                    <i class="lni lni-download"></i> Export List
                </button>
            </div>
        </div>
    </div>
</div>

<?php include '../assets/common/scripts.php'; ?>

<script>
    // Store teacher logs data for JavaScript access
    const teacherLogsData = <?php echo json_encode($teacher_logs); ?>;

    function viewStudents(date, logIndex) {
        const log = teacherLogsData[logIndex];
        const formattedDate = new Date(date).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Update modal title
        document.getElementById('studentsModalLabel').innerHTML =
            `<i class="lni lni-users"></i> Students Who Attended - ${formattedDate}`;

        // Create content
        let content = `
            <div class="mb-20">
                <div class="alert alert-info" style="background-color: #e3f2fd; border-left: 4px solid #004AAD; padding: 15px; border-radius: 8px;">
                    <h6 style="color: #004AAD; margin-bottom: 10px;">
                        <i class="lni lni-information"></i> Class Summary for ${formattedDate}
                    </h6>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Total Entries:</strong> ${log.entries}
                        </div>
                        <div class="col-md-4">
                            <strong>Subjects:</strong> ${log.subjects}
                        </div>
                        <div class="col-md-4">
                            <strong>Courses:</strong> ${log.courses}
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead style="background-color: #f8f9fa;">
                        <tr>
                            <th style="font-size: 14px; font-weight: 600;"><i class="lni lni-user"></i> Student Name</th>
                            <th style="font-size: 14px; font-weight: 600;"><i class="lni lni-book"></i> Subject</th>
                            <th style="font-size: 14px; font-weight: 600;"><i class="lni lni-timer"></i> Time In</th>
                            <th style="font-size: 14px; font-weight: 600;"><i class="lni lni-checkmark-circle"></i> Status</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Add student rows
        log.students.forEach(student => {
            const statusColor = student.status === 'Present' ? '#4CAF50' : '#FFC300';
            const statusIcon = student.status === 'Present' ? 'lni-checkmark-circle' : 'lni-clock';

            content += `
                <tr>
                    <td style="font-size: 14px; font-weight: 500;">${student.name}</td>
                    <td style="font-size: 13px;"><span class="badge" style="background-color: #e3f2fd; color: #004AAD; padding: 4px 8px; border-radius: 4px;">${student.subject}</span></td>
                    <td style="font-size: 13px;">${student.time_in}</td>
                    <td>
                        <span class="badge" style="background-color: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px;">
                            <i class="lni ${statusIcon}"></i> ${student.status}
                        </span>
                    </td>
                </tr>
            `;
        });

        content += `
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card" style="background-color: #e8f5e8; border: 1px solid #4CAF50; border-radius: 8px; padding: 15px;">
                            <div class="text-center">
                                <h5 style="color: #4CAF50; margin: 0;">
                                    <i class="lni lni-checkmark-circle"></i> ${log.students.filter(s => s.status === 'Present').length}
                                </h5>
                                <small style="color: #666;">Present</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card" style="background-color: #fff8e1; border: 1px solid #FFC300; border-radius: 8px; padding: 15px;">
                            <div class="text-center">
                                <h5 style="color: #FFC300; margin: 0;">
                                    <i class="lni lni-clock"></i> ${log.students.filter(s => s.status === 'Late').length}
                                </h5>
                                <small style="color: #666;">Late</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Set content and show modal
        document.getElementById('modalContent').innerHTML = content;

        // Show modal using Bootstrap
        const modal = new bootstrap.Modal(document.getElementById('studentsModal'));
        modal.show();
    }

    function exportStudentList() {
        // Simple export functionality
        const modalContent = document.getElementById('modalContent');
        const table = modalContent.querySelector('table');

        if (table) {
            let csv = [];

            // Get headers
            const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
            csv.push(headers.join(','));

            // Get data rows
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            rows.forEach(row => {
                const cells = Array.from(row.querySelectorAll('td')).map(td => td.textContent.trim());
                csv.push(cells.join(','));
            });

            // Download CSV
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `class_attendance_${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);
        }
    }
</script>

</body>
</html>
