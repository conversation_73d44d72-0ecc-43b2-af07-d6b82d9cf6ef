<?php
    include '../core/dbcon.ini';
    include '../assets/common/header.php';
    include '../assets/common/design.php';
    
    $page_title = "Class Attendance";
    $current_page = "Student List";
    
    // Get subject ID from URL
    $subject_id = isset($_GET['subject_id']) ? (int)$_GET['subject_id'] : 1;
    
    // Hardcoded subjects data
    $subjects = [
        1 => [
            'subject_name' => 'Computer Programming 1',
            'subject_code' => 'CS101',
            'section' => 'BSIT-1A',
            'schedule' => 'MWF 8:00-9:00 AM',
            'room' => 'Room 201'
        ],
        2 => [
            'subject_name' => 'Database Management Systems',
            'subject_code' => 'CS201',
            'section' => 'BSIT-2B',
            'schedule' => 'TTH 10:00-11:30 AM',
            'room' => 'Room 305'
        ],
        3 => [
            'subject_name' => 'Web Development',
            'subject_code' => 'CS301',
            'section' => 'BSIT-3A',
            'schedule' => 'MWF 2:00-3:30 PM',
            'room' => 'Room 402'
        ],
        4 => [
            'subject_name' => 'Software Engineering',
            'subject_code' => 'CS401',
            'section' => 'BSIT-4A',
            'schedule' => 'TTH 1:00-2:30 PM',
            'room' => 'Room 501'
        ]
    ];
    
    $current_subject = $subjects[$subject_id] ?? $subjects[1];
    
    // Hardcoded students data for the selected subject
    $students = [
        [
            'id' => 1,
            'student_no' => '123456',
            'first_name' => 'Glenn Mark',
            'last_name' => 'Ruz',
            'middle_name' => 'S.',
            'course' => 'BSIT',
            'year' => '1'
        ],
        [
            'id' => 2,
            'student_no' => '728373',
            'first_name' => 'Joe Vert',
            'last_name' => 'Matienzo',
            'middle_name' => 'A.',
            'course' => 'BSIT',
            'year' => '1'
        ],
        [
            'id' => 3,
            'student_no' => '34455',
            'first_name' => 'John Ralph',
            'last_name' => 'Jordico',
            'middle_name' => 'B.',
            'course' => 'BSIT',
            'year' => '1'
        ],
        [
            'id' => 4,
            'student_no' => '45433',
            'first_name' => 'Nessa Mae',
            'last_name' => 'Mata',
            'middle_name' => 'C.',
            'course' => 'BSIT',
            'year' => '1'
        ],
        [
            'id' => 5,
            'student_no' => '344551',
            'first_name' => 'Jimwel',
            'last_name' => 'Manguiat',
            'middle_name' => 'D.',
            'course' => 'BSIT',
            'year' => '1'
        ]
    ];
    
    $current_date = date('Y-m-d');
    $current_time = date('h:i A');
?>

<body>
<?php include '../assets/common/title.php'; ?>

<div class="main-container" id="main-container">
    <?php include '../assets/common/sidebar.php'; ?>
</div>

<main class="main-wrapper">
    <?php require '../assets/common/topbar.php'; ?>
    
    <section class="section">
        <div class="container-fluid">
            <!-- ========== title-wrapper start ========== -->
            <div class="title-wrapper pt-10">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="title">
                            <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                        </div>
                    </div>
                    <!-- end col -->
                    <div class="col-md-6">
                        <div class="breadcrumb-wrapper">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="index.php">My Classes</a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php echo $current_subject['subject_code']; ?>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <!-- end col -->
                </div>
                <!-- end row -->

                <!-- Subject Info Card -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-style mb-30">
                            <div class="title d-flex flex-wrap justify-content-between align-items-center">
                                <div class="left">
                                    <h4 class="text-bold mb-10"><?php echo $current_subject['subject_name']; ?></h4>
                                    <p class="text-sm text-gray mb-5">
                                        <strong>Code:</strong> <?php echo $current_subject['subject_code']; ?> | 
                                        <strong>Section:</strong> <?php echo $current_subject['section']; ?> | 
                                        <strong>Room:</strong> <?php echo $current_subject['room']; ?>
                                    </p>
                                    <p class="text-sm text-gray">
                                        <strong>Schedule:</strong> <?php echo $current_subject['schedule']; ?>
                                    </p>
                                </div>
                                <div class="right">
                                    <p class="text-sm text-gray mb-5">
                                        <strong>Date:</strong> <?php echo date('F d, Y'); ?>
                                    </p>
                                    <p class="text-sm text-gray">
                                        <strong>Time:</strong> <span id="current-time"><?php echo $current_time; ?></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students Attendance List -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-style">
                            <div class="title d-flex flex-wrap justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2">Student Attendance</h6>
                                    <p class="text-sm text-gray">Mark attendance for today's class</p>
                                </div>
                                <div class="right">
                                    <button class="main-btn primary-btn btn-hover btn-sm" onclick="saveAttendance()">
                                        Save Attendance
                                    </button>
                                </div>
                            </div>
                            
                            <div class="table-wrapper table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th><h6>Student No.</h6></th>
                                            <th><h6>Name</h6></th>
                                            <th><h6>Course & Year</h6></th>
                                            <th class="text-center"><h6>Present</h6></th>
                                            <th class="text-center"><h6>Late</h6></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($students as $student): ?>
                                        <tr>
                                            <td>
                                                <p><?php echo $student['student_no']; ?></p>
                                            </td>
                                            <td>
                                                <p><?php echo $student['last_name'] . ', ' . $student['first_name'] . ' ' . $student['middle_name']; ?></p>
                                            </td>
                                            <td>
                                                <p><?php echo $student['course'] . ' - ' . $student['year']; ?></p>
                                            </td>
                                            <td class="text-center">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="present[]" value="<?php echo $student['student_no']; ?>" id="present_<?php echo $student['student_no']; ?>">
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="late[]" value="<?php echo $student['student_no']; ?>" id="late_<?php echo $student['student_no']; ?>">
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section> 
    <?php include '../assets/common/footer.php'; ?>
</main>
<?php include '../assets/common/scripts.php'; ?>

<script>
// Real-time clock
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
    });
    document.getElementById('current-time').textContent = timeString;
}

// Update time every second
setInterval(updateTime, 1000);

// Save attendance function
function saveAttendance() {
    const presentCheckboxes = document.querySelectorAll('input[name="present[]"]:checked');
    const lateCheckboxes = document.querySelectorAll('input[name="late[]"]:checked');
    
    const presentStudents = Array.from(presentCheckboxes).map(cb => cb.value);
    const lateStudents = Array.from(lateCheckboxes).map(cb => cb.value);
    
    // For now, just show an alert with the selected students
    let message = 'Attendance saved!\n\n';
    if (presentStudents.length > 0) {
        message += 'Present: ' + presentStudents.join(', ') + '\n';
    }
    if (lateStudents.length > 0) {
        message += 'Late: ' + lateStudents.join(', ');
    }
    
    if (presentStudents.length === 0 && lateStudents.length === 0) {
        message = 'No attendance marked!';
    }
    
    alert(message);
}

// Prevent checking both present and late for the same student
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                const studentNo = this.value;
                const isPresent = this.name === 'present[]';
                const otherCheckbox = document.querySelector(
                    `input[name="${isPresent ? 'late[]' : 'present[]'}"][value="${studentNo}"]`
                );
                if (otherCheckbox) {
                    otherCheckbox.checked = false;
                }
            }
        });
    });
});
</script>

</body>
</html>
