<?php
// Database setup for Class Teacher Monitoring System
require_once '../core/dbcon.ini';

try {
    // Create class_attendance_logs table for teacher activity tracking
    $createLogsTable = "
    CREATE TABLE IF NOT EXISTS class_attendance_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_id INT NOT NULL,
        subject_code VARCHAR(20) NOT NULL,
        subject_name VARCHAR(255) NOT NULL,
        student_id VARCHAR(20) NOT NULL,
        student_name VARCHAR(255) NOT NULL,
        course VARCHAR(100) NOT NULL,
        year VARCHAR(10) NOT NULL,
        attendance_status ENUM('present', 'late', 'absent') NOT NULL,
        attendance_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_teacher_date (teacher_id, attendance_date),
        INDEX idx_course_year (course, year),
        INDEX idx_subject (subject_code),
        FOREIGN KEY (teacher_id) REFERENCES useraccounts(uaid) ON DELETE CASCADE
    )";
    
    $db1->exec($createLogsTable);
    echo "✅ class_attendance_logs table created successfully<br>";

    // Create class_subjects table to link teachers with subjects
    $createSubjectsTable = "
    CREATE TABLE IF NOT EXISTS class_subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_id INT NOT NULL,
        subject_code VARCHAR(20) NOT NULL,
        subject_name VARCHAR(255) NOT NULL,
        course VARCHAR(100) NOT NULL,
        year VARCHAR(10) NOT NULL,
        section VARCHAR(50) NOT NULL,
        schedule VARCHAR(100),
        room VARCHAR(50),
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_teacher_subject (teacher_id, subject_code, course, year),
        FOREIGN KEY (teacher_id) REFERENCES useraccounts(uaid) ON DELETE CASCADE
    )";
    
    $db1->exec($createSubjectsTable);
    echo "✅ class_subjects table created successfully<br>";

    // Insert sample subjects for current teacher (if logged in)
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (isset($_SESSION['uaid'])) {
        $teacher_id = $_SESSION['uaid'];
        
        // Sample IT subjects data
        $sampleSubjects = [
            ['IT402', 'Quantitative Methods', 'BSIT', '3', '3rd Yr. BSIT', 'MWF 8:00-9:00 AM', 'Room 201'],
            ['IT624', 'System Integration and Architecture 2', 'BSIT', '3', '3rd Yr. BSIT', 'TTH 10:00-11:30 AM', 'Room 305'],
            ['IT271', 'System Administration and Maintenance', 'BSIT', '3', '3rd Yr. BSIT', 'MWF 2:00-3:30 PM', 'Room 402'],
            ['IT421', 'Software Engineering with Quality Testing and Evaluation', 'BSIT', '3', '3rd Yr. BSIT', 'TTH 1:00-2:30 PM', 'Room 501'],
            ['IT291', 'Social Issues and IT Professional Practice', 'BSIT', '3', '3rd Yr. BSIT', 'MWF 11:00-12:00 PM', 'Room 301'],
            ['IT621', 'Integrative Programming Technologies 2', 'BSIT', '3', '3rd Yr. BSIT', 'TTH 3:00-4:30 PM', 'Room 401'],
            ['IT431', 'IT Capstone Project 1', 'BSIT', '3', '3rd Yr. BSIT', 'MWF 1:00-2:00 PM', 'Room 502'],
            ['IT321', 'System Integration and Architecture 1', 'BSIT', '3', '3rd Yr. BSIT', 'TTH 8:00-9:30 AM', 'Room 203'],
            ['NSTP2', 'Civic Welfare Training Service', 'BSIT', '3', '3rd Yr. BSIT', 'SAT 7:00-10:00 AM', 'Room 101'],
            ['IT106', 'Application Development and Emerging Technologies', 'BSIT', '3', '3rd Yr. BSIT', 'MWF 3:00-4:00 PM', 'Room 404'],
            ['IT221', 'Multimedia and Graphics Designing 1', 'BSIT', '3', '3rd Yr. BSIT', 'TTH 2:00-3:30 PM', 'Room 302'],
            ['IT241', 'Advanced Database Management', 'BSIT', '3', '3rd Yr. BSIT', 'MWF 9:00-10:00 AM', 'Room 205'],
            ['IT311', 'Information Assurance and Security 1', 'BSIT', '3', '3rd Yr. BSIT', 'TTH 4:00-5:30 PM', 'Room 503']
        ];

        $stmt = $db1->prepare("INSERT IGNORE INTO class_subjects (teacher_id, subject_code, subject_name, course, year, section, schedule, room) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($sampleSubjects as $subject) {
            $stmt->execute([
                $teacher_id,
                $subject[0], // subject_code
                $subject[1], // subject_name
                $subject[2], // course
                $subject[3], // year
                $subject[4], // section
                $subject[5], // schedule
                $subject[6]  // room
            ]);
        }
        
        echo "✅ Sample subjects inserted for teacher ID: $teacher_id<br>";
    }

    // Create some sample attendance logs for demonstration
    if (isset($_SESSION['uaid'])) {
        $teacher_id = $_SESSION['uaid'];
        
        // Get some students for sample data
        $studentsStmt = $db1->prepare("SELECT Student_ID, CONCAT(First_Name, ' ', Last_Name) as full_name, Course, Year FROM cdas_student LIMIT 10");
        $studentsStmt->execute();
        $students = $studentsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($students)) {
            $logStmt = $db1->prepare("INSERT IGNORE INTO class_attendance_logs (teacher_id, subject_code, subject_name, student_id, student_name, course, year, attendance_status, attendance_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
            // Create logs for the past 7 days
            for ($i = 0; $i < 7; $i++) {
                $date = date('Y-m-d', strtotime("-$i days"));
                
                foreach ($students as $student) {
                    // Random subject from our list
                    $subjects = ['IT402', 'IT624', 'IT271', 'IT421'];
                    $subjectNames = [
                        'IT402' => 'Quantitative Methods',
                        'IT624' => 'System Integration and Architecture 2',
                        'IT271' => 'System Administration and Maintenance',
                        'IT421' => 'Software Engineering with Quality Testing and Evaluation'
                    ];
                    
                    $randomSubject = $subjects[array_rand($subjects)];
                    $statuses = ['present', 'late', 'present', 'present']; // More present than late
                    $randomStatus = $statuses[array_rand($statuses)];
                    
                    $logStmt->execute([
                        $teacher_id,
                        $randomSubject,
                        $subjectNames[$randomSubject],
                        $student['Student_ID'],
                        $student['full_name'],
                        $student['Course'],
                        $student['Year'],
                        $randomStatus,
                        $date
                    ]);
                }
            }
            
            echo "✅ Sample attendance logs created<br>";
        }
    }

    echo "<br>🎉 Database setup completed successfully!<br>";
    echo "<a href='index.php'>← Back to Class Dashboard</a><br>";
    echo "<a href='monitoring.php'>→ Go to Monitoring</a>";

} catch(PDOException $e) {
    echo "❌ Error setting up database: " . $e->getMessage();
}
?>
