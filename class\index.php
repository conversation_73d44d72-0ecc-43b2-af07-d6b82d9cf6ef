<?php
    include '../core/dbcon.ini';
    include '../assets/common/header.php';
    include '../assets/common/design.php';

    $page_title = "Class Teacher Dashboard";
    $current_page = "My Classes";

    // Hardcoded subjects data for now (Google Classroom style)
    $subjects = [
        [
            'id' => 1,
            'subject_name' => 'Computer Programming 1',
            'subject_code' => 'CS101',
            'section' => 'BSIT-1A',
            'schedule' => 'MWF 8:00-9:00 AM',
            'room' => 'Room 201',
            'students_count' => 35,
            'color' => 'purple'
        ],
        [
            'id' => 2,
            'subject_name' => 'Database Management Systems',
            'subject_code' => 'CS201',
            'section' => 'BSIT-2B',
            'schedule' => 'TTH 10:00-11:30 AM',
            'room' => 'Room 305',
            'students_count' => 28,
            'color' => 'success'
        ],
        [
            'id' => 3,
            'subject_name' => 'Web Development',
            'subject_code' => 'CS301',
            'section' => 'BSIT-3A',
            'schedule' => 'MWF 2:00-3:30 PM',
            'room' => 'Room 402',
            'students_count' => 32,
            'color' => 'orange'
        ],
        [
            'id' => 4,
            'subject_name' => 'Software Engineering',
            'subject_code' => 'CS401',
            'section' => 'BSIT-4A',
            'schedule' => 'TTH 1:00-2:30 PM',
            'room' => 'Room 501',
            'students_count' => 25,
            'color' => 'blue'
        ]
    ];
?>

<style>
.icon-small.purple { background-color: #6f42c1; }
.icon-small.success { background-color: #28a745; }
.icon-small.orange { background-color: #fd7e14; }
.icon-small.blue { background-color: #007bff; }
.card-style:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
</style>

<body>
<?php include '../assets/common/title.php'; ?>

<div class="main-container" id="main-container">
    <?php include '../assets/common/sidebar.php'; ?>
</div>

<main class="main-wrapper">
    <?php require '../assets/common/topbar.php'; ?>

    <section class="section">
        <div class="container-fluid">
            <!-- ========== title-wrapper start ========== -->
            <div class="title-wrapper pt-10">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="title">
                            <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                        </div>
                    </div>
                    <!-- end col -->
                    <div class="col-md-6">
                        <div class="breadcrumb-wrapper">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="#0">Dashboard</a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php echo isset($current_page) ? $current_page : ''; ?>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <!-- end col -->
                </div>
                <!-- end row -->

                <!-- Subject Cards Grid -->
                <div class="row">
                    <?php foreach($subjects as $subject): ?>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="card-style mb-20" style="cursor: pointer; min-height: 120px; padding: 15px;" onclick="window.location.href='students.php?subject_id=<?php echo $subject['id']; ?>'">
                            <div class="d-flex align-items-center mb-10">
                                <div class="icon-small <?php echo $subject['color']; ?> me-2" style="width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="lni lni-graduation" style="font-size: 14px; color: white;"></i>
                                </div>
                                <h6 class="mb-0 text-bold" style="font-size: 14px;"><?php echo $subject['subject_code']; ?></h6>
                            </div>
                            <h6 class="mb-5" style="font-size: 13px; line-height: 1.3;"><?php echo $subject['subject_name']; ?></h6>
                            <p class="text-sm text-gray mb-5" style="font-size: 11px;">
                                <?php echo $subject['section']; ?>
                            </p>
                            <p class="text-sm text-gray" style="font-size: 11px;">
                                <i class="lni lni-users"></i> <?php echo $subject['students_count']; ?> students
                            </p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <!-- end subjects grid -->

            </div>
        </div>
    </section>
    <?php include '../assets/common/footer.php'; ?>
</main>
<?php include '../assets/common/scripts.php'; ?>

</body>
</html>