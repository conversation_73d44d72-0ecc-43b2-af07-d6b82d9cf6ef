<?php
    include '../core/dbcon.ini';
    include '../assets/common/header.php';
    include '../assets/common/design.php';

    $page_title = "Class Teacher Dashboard";
    $current_page = "My Classes";

    // Real IT subjects data with specific icons
    $subjects = [
        [
            'id' => 1,
            'subject_name' => 'Quantitative Methods',
            'subject_code' => 'IT402',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 8:00-9:00 AM',
            'room' => 'Room 201',
            'students_count' => 35,
            'color' => 'purple',
            'icon' => 'lni-calculator'
        ],
        [
            'id' => 2,
            'subject_name' => 'System Integration and Architecture 2',
            'subject_code' => 'IT624',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 10:00-11:30 AM',
            'room' => 'Room 305',
            'students_count' => 28,
            'color' => 'success',
            'icon' => 'lni-network'
        ],
        [
            'id' => 3,
            'subject_name' => 'System Administration and Maintenance',
            'subject_code' => 'IT271',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 2:00-3:30 PM',
            'room' => 'Room 402',
            'students_count' => 32,
            'color' => 'orange',
            'icon' => 'lni-cog'
        ],
        [
            'id' => 4,
            'subject_name' => 'Software Engineering with Quality Testing and Evaluation',
            'subject_code' => 'IT421',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 1:00-2:30 PM',
            'room' => 'Room 501',
            'students_count' => 25,
            'color' => 'blue',
            'icon' => 'lni-code'
        ],
        [
            'id' => 5,
            'subject_name' => 'Social Issues and IT Professional Practice',
            'subject_code' => 'IT291',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 11:00-12:00 PM',
            'room' => 'Room 301',
            'students_count' => 30,
            'color' => 'purple',
            'icon' => 'lni-users'
        ],
        [
            'id' => 6,
            'subject_name' => 'Integrative Programming Technologies 2',
            'subject_code' => 'IT621',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 3:00-4:30 PM',
            'room' => 'Room 401',
            'students_count' => 27,
            'color' => 'success',
            'icon' => 'lni-laptop'
        ],
        [
            'id' => 7,
            'subject_name' => 'IT Capstone Project 1',
            'subject_code' => 'IT431',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 1:00-2:00 PM',
            'room' => 'Room 502',
            'students_count' => 22,
            'color' => 'orange',
            'icon' => 'lni-rocket'
        ],
        [
            'id' => 8,
            'subject_name' => 'System Integration and Architecture 1',
            'subject_code' => 'IT321',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 8:00-9:30 AM',
            'room' => 'Room 203',
            'students_count' => 33,
            'color' => 'blue',
            'icon' => 'lni-layers'
        ],
        [
            'id' => 9,
            'subject_name' => 'Civic Welfare Training Service',
            'subject_code' => 'NSTP2',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'SAT 7:00-10:00 AM',
            'room' => 'Room 101',
            'students_count' => 40,
            'color' => 'purple',
            'icon' => 'lni-heart'
        ],
        [
            'id' => 10,
            'subject_name' => 'Application Development and Emerging Technologies',
            'subject_code' => 'IT106',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 3:00-4:00 PM',
            'room' => 'Room 404',
            'students_count' => 29,
            'color' => 'success',
            'icon' => 'lni-mobile'
        ],
        [
            'id' => 11,
            'subject_name' => 'Multimedia and Graphics Designing 1',
            'subject_code' => 'IT221',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 2:00-3:30 PM',
            'room' => 'Room 302',
            'students_count' => 26,
            'color' => 'orange',
            'icon' => 'lni-brush'
        ],
        [
            'id' => 12,
            'subject_name' => 'Advanced Database Management',
            'subject_code' => 'IT241',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 9:00-10:00 AM',
            'room' => 'Room 205',
            'students_count' => 31,
            'color' => 'blue',
            'icon' => 'lni-database'
        ],
        [
            'id' => 13,
            'subject_name' => 'Information Assurance and Security 1',
            'subject_code' => 'IT311',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 4:00-5:30 PM',
            'room' => 'Room 503',
            'students_count' => 24,
            'color' => 'purple',
            'icon' => 'lni-shield'
        ]
    ];

    // Define colors for inline styles
    $colors = [
        'purple' => '#6f42c1',
        'success' => '#28a745',
        'orange' => '#fd7e14',
        'blue' => '#007bff'
    ];
?>

<body>
<?php include '../assets/common/title.php'; ?>

<div class="main-container" id="main-container">
    <?php include '../assets/common/sidebar.php'; ?>
</div>

<main class="main-wrapper">
    <?php require '../assets/common/topbar.php'; ?>

    <section class="section">
        <div class="container-fluid">
            <!-- ========== title-wrapper start ========== -->
            <div class="title-wrapper pt-10">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="title">
                            <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                        </div>
                    </div>
                    <!-- end col -->
                    <div class="col-md-6">
                        <div class="breadcrumb-wrapper">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="#0">Dashboard</a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php echo isset($current_page) ? $current_page : ''; ?>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <!-- end col -->
                </div>
                <!-- end row -->

                <!-- Class Teacher Menu -->
                <div class="row mb-30">
                    <div class="col-12">
                        <div class="card-style">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2" style="font-size: 16px;">Class Teacher Tools</h6>
                                    <p class="text-sm text-gray" style="font-size: 14px;">Quick access to monitoring and reports</p>
                                </div>
                                <div class="right d-flex gap-3">
                                    <a href="monitoring.php" class="main-btn primary-btn btn-hover" style="background-color: #004AAD; border-color: #004AAD; border-radius: 8px; padding: 12px 24px; font-size: 14px; text-decoration: none; color: white; font-weight: 500;">
                                        📊 Monitoring & Logs
                                    </a>
                                    <a href="get_attendance.php" class="main-btn success-btn btn-hover" style="background-color: #4CAF50; border-color: #4CAF50; border-radius: 8px; padding: 12px 24px; font-size: 14px; text-decoration: none; color: white; font-weight: 500;">
                                        📋 Get Attendance
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subject Cards Grid -->
                <div class="row">
                    <?php foreach($subjects as $subject): ?>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="card-style mb-20 d-flex flex-column" style="cursor: pointer; height: 160px; padding: 18px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.1)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='';" onclick="window.location.href='students.php?subject_id=<?php echo $subject['id']; ?>'">
                            <div class="d-flex align-items-center mb-12">
                                <div class="me-3" style="width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: <?php echo $colors[$subject['color']]; ?>;">
                                    <i class="lni <?php echo $subject['icon']; ?>" style="font-size: 16px; color: white;"></i>
                                </div>
                                <h5 class="mb-0 text-bold" style="font-size: 16px;"><?php echo $subject['subject_code']; ?></h5>
                            </div>
                            <div class="flex-grow-1 d-flex flex-column">
                                <h6 class="mb-8" style="font-size: 14px; line-height: 1.3; font-weight: 500; height: 42px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;"><?php echo $subject['subject_name']; ?></h6>
                                <div class="mt-auto">
                                    <p class="text-gray mb-6" style="font-size: 13px;">
                                        <?php echo $subject['section']; ?>
                                    </p>
                                    <p class="text-gray mb-0" style="font-size: 12px;">
                                        <i class="lni lni-users"></i> <?php echo $subject['students_count']; ?> students
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <!-- end subjects grid -->

            </div>
        </div>
    </section>
    <?php include '../assets/common/footer.php'; ?>
</main>
<?php include '../assets/common/scripts.php'; ?>

</body>
</html>