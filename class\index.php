<?php
    include '../core/dbcon.ini';
    include '../assets/common/header.php';
    include '../assets/common/design.php';

    $page_title = "Class Teacher Dashboard";
    $current_page = "My Classes";

    // Hardcoded subjects data for now (Google Classroom style)
    $subjects = [
        [
            'id' => 1,
            'subject_name' => 'Computer Programming 1',
            'subject_code' => 'CS101',
            'section' => 'BSIT-1A',
            'schedule' => 'MWF 8:00-9:00 AM',
            'room' => 'Room 201',
            'students_count' => 35,
            'color' => 'purple'
        ],
        [
            'id' => 2,
            'subject_name' => 'Database Management Systems',
            'subject_code' => 'CS201',
            'section' => 'BSIT-2B',
            'schedule' => 'TTH 10:00-11:30 AM',
            'room' => 'Room 305',
            'students_count' => 28,
            'color' => 'success'
        ],
        [
            'id' => 3,
            'subject_name' => 'Web Development',
            'subject_code' => 'CS301',
            'section' => 'BSIT-3A',
            'schedule' => 'MWF 2:00-3:30 PM',
            'room' => 'Room 402',
            'students_count' => 32,
            'color' => 'orange'
        ],
        [
            'id' => 4,
            'subject_name' => 'Software Engineering',
            'subject_code' => 'CS401',
            'section' => 'BSIT-4A',
            'schedule' => 'TTH 1:00-2:30 PM',
            'room' => 'Room 501',
            'students_count' => 25,
            'color' => 'blue'
        ]
    ];
?>

<body>
<?php include '../assets/common/title.php'; ?>

<div class="main-container" id="main-container">
    <?php include '../assets/common/sidebar.php'; ?>
</div>

<main class="main-wrapper">
    <?php require '../assets/common/topbar.php'; ?>

    <section class="section">
        <div class="container-fluid">
            <!-- ========== title-wrapper start ========== -->
            <div class="title-wrapper pt-10">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="title">
                            <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                        </div>
                    </div>
                    <!-- end col -->
                    <div class="col-md-6">
                        <div class="breadcrumb-wrapper">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="#0">Dashboard</a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php echo isset($current_page) ? $current_page : ''; ?>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <!-- end col -->
                </div>
                <!-- end row -->

                <!-- Subject Cards Grid -->
                <div class="row">
                    <?php foreach($subjects as $subject): ?>
                    <div class="col-xl-3 col-lg-4 col-sm-6">
                        <div class="icon-card mb-30" style="cursor: pointer;" onclick="window.location.href='students.php?subject_id=<?php echo $subject['id']; ?>'">
                            <div class="icon <?php echo $subject['color']; ?>">
                                <i class="lni lni-graduation"></i>
                            </div>
                            <div class="content">
                                <h6 class="mb-10"><?php echo $subject['subject_name']; ?></h6>
                                <h5 class="text-bold mb-5"><?php echo $subject['subject_code']; ?></h5>
                                <p class="text-sm text-gray mb-5">
                                    <strong>Section:</strong> <?php echo $subject['section']; ?>
                                </p>
                                <p class="text-sm text-gray mb-5">
                                    <strong>Schedule:</strong> <?php echo $subject['schedule']; ?>
                                </p>
                                <p class="text-sm text-gray mb-5">
                                    <strong>Room:</strong> <?php echo $subject['room']; ?>
                                </p>
                                <p class="text-sm text-success">
                                    <i class="lni lni-users"></i> <?php echo $subject['students_count']; ?>
                                    <span class="text-gray">Students</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <!-- end subjects grid -->

            </div>
        </div>
    </section>
    <?php include '../assets/common/footer.php'; ?>
</main>
<?php include '../assets/common/scripts.php'; ?>

</body>
</html>